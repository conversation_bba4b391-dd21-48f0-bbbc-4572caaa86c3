import React from "react";
import { Link } from "react-router-dom";

export default function Navbar() {
  return (
    <nav className="bg-ubud-cream shadow-lg px-6 py-4 flex justify-between items-center sticky top-0 z-50">
      <div className="flex items-center">
        <div className="text-ubud-dark-green font-bold text-2xl">
          <span className="text-ubud-yellow">Ubud</span> Activity
        </div>
      </div>
      <div className="flex items-center space-x-8">
        <Link to="/" className="text-ubud-dark-green hover:text-ubud-light-green font-medium transition-colors">
          HOME
        </Link>
        <Link to="/services" className="text-ubud-dark-green hover:text-ubud-light-green font-medium transition-colors">
          BOOKING EXPERIENCE
        </Link>
        <Link to="/about" className="text-ubud-dark-green hover:text-ubud-light-green font-medium transition-colors">
          ABOUT US
        </Link>
        <Link to="/booking" className="text-ubud-dark-green hover:text-ubud-light-green font-medium transition-colors">
          BOOKING
        </Link>
        <Link
          to="/login"
          className="bg-ubud-dark-green text-white px-6 py-2 rounded-full hover:bg-ubud-light-green transition-colors font-medium"
        >
          Sign In
        </Link>
      </div>
    </nav>
  );
}
