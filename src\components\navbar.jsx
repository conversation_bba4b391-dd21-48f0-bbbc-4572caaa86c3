import React from "react";
import { Link } from "react-router-dom";

export default function Navbar() {
  return (
    <nav className="bg-white shadow p-4 flex justify-between items-center sticky top-0 z-50">
      <div className="text-green-700 font-bold text-xl">Ubud Activity</div>
      <div className="space-x-4">
        <Link to="/" className="text-gray-700 hover:text-green-700">Home</Link>
        <Link to="/booking" className="text-gray-700 hover:text-green-700">Booking</Link>
        <Link to="/services" className="text-gray-700 hover:text-green-700">Services</Link>
        <Link to="/about" className="text-gray-700 hover:text-green-700">About</Link>
        <Link to="/login" className="bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700">Sign In</Link>
      </div>
    </nav>
  );
}
